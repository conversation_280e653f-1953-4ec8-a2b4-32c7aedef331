@use '../../utils' as *;

/*----------------------------------------*/
/*  5.2 Postbox css
/*----------------------------------------*/

// postbox css start

.postbox{
    &__thumb{
        position: relative;
        & .play-btn{
            position: absolute;
            top: 38%;
            left: 44%;
            z-index: 1;
            & a{
                height: 85px;
                width: 85px;
                line-height: 87px;
                &::after{
                    display: none;
                }
            }
        }
    }
    &__item{
        &-single{
            &:hover{
                box-shadow: none;
            }
        }
    }
    &__tag{
        position: absolute;
        bottom: 0px;
        left: 0;
        & p{
            margin-bottom: 0;
            font-weight: 700;
            font-size: 12px;
            text-align: center;
            text-transform: uppercase;
            color: #000;
            letter-spacing: 0.135em;
            background: var(--tp-theme-1);
            clip-path: polygon(0px 0px, 100% 0px, 92.7% 53.45%, 100% 100%, 0px 100%, 0px 50%);
            width: 130px;
        }
    }
    &__content{
        padding-top: 28px;
        &-single{
            padding-left: 0;
            padding-right: 0;
            border: none;
        }
    }
    &__title{
        font-size: 56px;
        font-weight: 600;
        line-height: 1;
        letter-spacing: -2.24px;
        color: var(--tp-common-black);
        margin-bottom: 10px;
        @media #{$lg,$md,$sm}{
            font-size: 30px;
        }
        @media #{$xs}{
            font-size: 25px;
        }
        & a{
            background-image: -webkit-radial-gradient(#000, #000), -webkit-radial-gradient(#000, #000);
            background-image: -moz-radial-gradient(#000, #000), -moz-radial-gradient(#000, #000);
            background-image: -ms-radial-gradient(#000, #000), -ms-radial-gradient(#000, #000);
            background-image: -o-radial-gradient(#000, #000), -o-radial-gradient(#000, #000);
            background-image: radial-gradient(#000, #000), radial-gradient(#000, #000);
            background-size: 0% 1px, 0 1px;
            background-position: 100% 100%, 0 91%;
            background-repeat: no-repeat;
            &:hover{
                background-size: 0 1px, 100% 1px;
            }
        }
    }
    &__meta{
        margin-bottom: 5px;
        & span{
            color: #414145;
            font-size: 16px;
            font-weight: 500;
            line-height: 1;
            text-transform: uppercase;
        }
    }
    &__blockquote{
        background: #F4F6F8;
        padding: 50px 65px;
        @media #{$xs}{
            padding: 20px;
        }
        &-icon{
            margin-bottom: 20px;
            display: inline-block;
        }
        & p{
            font-size: 24px;
            font-weight: 600;
            line-height: 32px;
            text-transform: uppercase;
            color: var(--tp-common-black);
            margin-bottom: 15px;
            @media #{$xs}{
                font-size: 19px;
                font-weight: 28px;
            }
        }
        &-info{
            color: #5D5D63;
            font-size: 14px;
            font-weight: 600;
            line-height: 32px;
        }
    }
    &-details-desc-thumb-caption{
        font-size: 18px;
        font-weight: 500;
        color: #807A7A;
        font-style: italic;
        text-align: center;
        display: block;
        margin-top: 30px;
        margin-bottom: 50px;
    }
    &__list{
        margin-bottom: 60px;
        &-title{
            font-weight: 600;
            font-size: 28px;
            color: #121D2C;
            margin-bottom: 50px;
        }
        &-content{
            & ul{
                & li{
                    list-style: none;
                    font-weight: 400;
                    font-size: 16px;
                    color: #445658;
                    margin-bottom: 24px;
                    position: relative;
                    padding-left: 40px;
                    & span{
                        height: 27px;
                        width: 27px;
                        line-height: 25px;
                        border-radius: 50%;
                        display: inline-block;
                        text-align: center;
                        background-color: var(--tp-common-white);
                        color: var(--tp-theme-1);
                        box-shadow: 0px 0px 9px rgba(0, 0, 0, 0.06);
                        position: absolute;
                        top: 0;
                        left: 0;
                        &.active{
                            background-color: var(--tp-theme-1);
                            color: var(--tp-common-white);
                        }
                    }
                }
            }
        }
    }
    &__details{
        &-share{
            &-wrapper{
                padding-top: 20px;
                padding-bottom: 30px;
                border-bottom: 1px solid #F7F7F7;
            }
            & span{
                font-size: 20px;
                font-weight: 500;
                color: #121416;
                margin-right: 15px;
            }
            & a{
                height: 37px;
                width: 37px;
                text-align: center;
                line-height: 37px;
                display: inline-block;
                background-color: #F2F6F7;
                color: var(--tp-common-black);
                margin-right: 10px;
                transition: all .3s ease-in-out;
                @media #{$lg} {
                    margin-bottom: 10px;
                    height: 30px;
                    width: 30px;
                    line-height: 30px;
                    margin-right: 8px;
                }
                &:hover{
                    background-color: var(--tp-theme-1);
                    color: var(--tp-common-white);
                }
            }
        }
        &-tag{
            & span{
                font-size: 20px;
                font-weight: 500;
                color: #121416;
                margin-right: 6px;
            }
        }
    }
    &__read-more{
        & .postbox-btn{
            position: relative;
            font-weight: 500;
            font-size: 14px;
            text-transform: uppercase;
            color: #132047;
            & span{
                margin-left: 5px;
                &::before{
                    position: absolute;
                    content: "";
                    top: 1px;
                    right: -10px;
                    border: 1px solid #E8F2F9;
                    height: 28px;
                    width: 28px;
                    display: inline-block;
                    border-radius: 50%;
                }
            }
        }
    }
    &__text{
        & img{
            max-width: 100%;
        }
        & p{
            color: #5D5D63;
            font-size: 17px;
            font-weight: 400;
            line-height: 26px;
            margin-bottom: 30px;
        }
        &-single{
            & p{
                margin-bottom: 15px;
            }
        }
    }
    &__slider{
        & button{
            position: absolute;
            left: 50px;
            top: 50%;
            @include transform(translateY(-50%));
            z-index: 1;
            font-size: 30px;
            color: var(--tp-common-white);
            &.postbox-slider-button-next{
                left: auto;
                right: 50px;
                @media #{$xs}{
                    right: 10px;
                }
            }
            @media #{$xs}{
                left: 10px;
            }
        }
    }
    &__comment{
        & ul{
            & li{
                margin-bottom: 45px;
                list-style: none;

                &.children{
                    margin-left: 65px;
                    @media #{$xs}{
                        margin-left: 15px;
                    }
                }
            }
        }
        &-form{
            margin-bottom: 40px;
            padding: 65px 45px 80px 45px;
            background: #F8F8F9;
            @media #{$xs} {
                padding: 20px;
            }
            &-title{
                font-size: 26px;
                font-weight: 600;
                margin-bottom: 40px;
            }
        }
        &-input{
            position: relative;
            margin-bottom: 20px;
            & span{
                font-weight: 600;
                color: var(--tp-common-black);
                margin-bottom: 12px;
                display: block;
            }

            & input,
            & textarea{
                height: 55px;
                padding: 0 20px;
                width: 100%;
                font-size: 14px;
                color: var(--tp-common-black);
                outline: none;
                background: #FFFFFF;
                border: 1px solid #E5E5E5;
                box-shadow: 0px 15px 10px rgba(242, 242, 242, 0.18);
                &:focus{
                    border: 1px solid var(--tp-theme-1);
                    &::placeholder{
                        font-size: 0;
                    }
                }
            }
            & textarea{
                height: 175px;
                resize: none;
                padding-top: 20px;
                padding-bottom: 20px;
            }
        }
        &-title{
            font-size: 28px;
            color: #121416;
            font-weight: 600;
            margin-bottom: 20px;
        }
        &-box{
            padding-top: 20px;
            @media #{$xs}{
                flex-wrap: wrap;
            }
        }
        &-info{
            flex: 0 0 auto;
        }
        &-avater{
            @media #{$xs}{
                margin-right: 0;
            }
            & img{
                width: 90px;
                height: 90px;
                @media #{$xs}{
                    margin-bottom: 30px;
                }
            }
        }
        &-name{
            margin-bottom: 17px;

            & h5{
                font-size: 20px;
                font-weight: 700;
                line-height: 1;
                letter-spacing: -0.4px;
                color: var(--tp-common-black);
                margin-bottom: 0;
            }
            & span{
                color: #414144;
                font-size: 17px;
                font-weight: 400;
                line-height: 1;
            }
        }
        &-text{
            & p{
                color: #5D5D63;
                font-size: 17px;
                font-weight: 400;
                line-height: 26px;
            }
        }
        &-reply{
            & a{
                color: #19191A;
                font-size: 15px;
                font-weight: 600;
                line-height: 1;
                padding: 5px 20px;
                border: 1px solid #E0E2E3;
                &:hover{
                    background-color: var(--tp-common-black);
                    color: var(--tp-common-white);
                    border-color: var(--tp-common-white);
                }
            }
        }
        &-agree{
            padding-left: 5px;
            & input{
                margin: 0;
                appearance: none;
                -moz-appearance: none;
                display: block;
                width: 16px;
                height: 16px;
                background: var(--tp-common-white);
                border: 1px solid #949392;
                outline: none;
                flex : 0 0 auto;
                @include transform(translateY(-1px));
                &:checked{
                    position: relative;
                    background-color: var(--tp-theme-1);
                    border-color: transparent;
                    &::after{
                        box-sizing: border-box;
                        content: '\f00c';
                        position: absolute;
                        font-family: var(--tp-ff-fontawesome);
                        font-size: 10px;
                        color: var(--tp-common-white);
                        top: 47%;
                        left: 50%;
                        @include transform(translate(-50%, -50%));
                    }
                }
                &:hover{
                    cursor: pointer;
                }
            }
            & label{
                padding-left: 8px;
                color: #838383;
                line-height: 1;
                & a{
                    color: var(--tp-common-black);
                    font-weight: 600;
                    padding-left: 4px;
                    &:hover{
                        color: var(--tp-theme-1);
                    }
                }
                &:hover{
                    cursor: pointer;
                }
            }
        }
    }
    &__tag{
        & span{
            font-size: 16px;
            margin-bottom: 17px;
            color: var(--tp-common-black);
            margin-right: 10px;
        }
    }
    &__play-btn{
        & a{
            height: 66px;
            width: 66px;
            line-height: 66px;
            position: absolute;
            top: 50%;
            left: 50%;
            text-align: center;
            border-radius: 50%;
            animation: pulse 2s infinite;
            transform: translate(-50%,-50%);
            color: var(--tp-common-white);
            border: 2px solid var(--tp-common-white);
        }
    }
}
.postbox{
    &__area{
        @media #{$xs}{
            padding-top: 60px;
        }
    }
    &__link-post{
        &-wrap{
            background-color: #F9F9F9;
            padding: 60px;
            @media #{$xs}{
                padding: 20px;
                flex-wrap: wrap;
            }
            & p{
                margin-bottom: 0;
                font-size: 24px;
                font-weight: 600;
                line-height: 34px;
                color: var(--tp-common-black);
                @media #{$lg,$md}{
                    & br{
                        display: none;
                    }
                }
                @media #{$xs}{
                    font-size: 19px;
                    font-weight: 28px;
                    & br{
                        display: none;
                    }
                }
            }          
        }
        &-icon{
            margin-right: 40px;
            display: inline-block;
            @media #{$xs}{
                margin-bottom: 10px;
                margin-right: 0;
            }
        }
    }
    &__slider-arrow-wrap{
        & button{
            height: 50px;
            width: 50px;
            line-height: 50px;
            text-align: center;
            font-size: 18px;
            border-radius: 50%;
            color: var(--tp-common-white);
            border: 1px solid var(--tp-common-white);
            transition: .3s;
            &:hover{
                background-color: var(--tp-common-white);
                color: var(--tp-common-black);
            }
        }
        & .postbox-arrow-prev{
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 30px;
            z-index: 9;
        }
        & .postbox-arrow-next{
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 30px;
            z-index: 9;
        }
    }
}
.basic-pagination{
    margin-bottom: 40px;
    & ul{
        & li{
            list-style: none;
            display: inline-block;
            margin-right: 14px;
            @media #{$xs} {
                margin-right: 5px;
            }
            & a{
                height: 46px;
                width: 30px;
                border-radius: 200px;
                display: inline-block;
                line-height: 40px;
                text-align: center;
                font-size: 20px;
                font-weight: 600;
                transition: all .3s ease-in-out;
                color: var(--tp-common-black);
                & .current{
                    height: 100%;
                    width: 100%;
                    display: inline-block;
                    border-radius: 200px;
                    border: 2px solid var(--tp-common-black);
                }
                & .icon{
                    height: 46px;
                    width: 46px;
                    line-height: 46px;
                    border-radius: 50%;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: center;
                    display: inline-block;
                    border-radius: 100px;
                    border: 1px solid rgba(25, 25, 26, 0.20);
                    transition: .3s;
                    &:hover{
                        color: var(--tp-common-white);
                        border-color: var(--tp-common-black);
                        background-color: var(--tp-common-black);
                    }
                }
            }
        }
    }
}

.tp-postbox-details{
    &-remeber {
        display: flex;
        align-items: start;
    }
    &-form{
        & p{
            color: #5D5D63;
            font-size: 17px;
            font-weight: 400;
            line-height: 26px;
            @media #{$xs}{
                font-size: 16px;
            }
        }
        &-title{
            color: #19191A;
            font-size: 34px;
            font-weight: 600;
            line-height: 1;
            letter-spacing: -0.68px;
        }
    }
    &-input{
        & input{
            height: 50px;
            line-height: 50px;
            border: none;
            border: 1px solid rgba(25, 25, 26, 0.10);
            font-size: 17px;
            font-weight: 400;
            color: var(--tp-common-black);
            margin-bottom: 20px;
            @include placeholder{
                font-size: 17px;
                font-weight: 400;
                color: #707072;
            }
            &::focus{
                border-color: var(--tp-common-black);
            }
        }
        & textarea{
            height: 160px;
            font-size: 17px;
            font-weight: 400;
            resize: none;
            margin-bottom: 20px;
            color: var(--tp-common-black);
            @include placeholder{
                font-size: 17px;
                font-weight: 400;
                color: #707072;
            }
            &::focus{
                border-color: var(--tp-common-black);
            }
        }
    }
    &-remeber{
        & input{
            margin-right: 10px;
            transform: translateY(7px);
        }
        & label{
            color: #5D5D63;
            font-size: 16px;
            font-weight: 400;
            line-height: 26px;
            cursor: pointer;
        }
    }
}
