@use '../utils' as *;

/*----------------------------------------*/
/*  2.7 Carousel
/*----------------------------------------*/

/* tp range slider css */
.#{$theme-prifix}-range-slider{

    & .inside-slider{
        padding-left: 7px;
        padding-right: 17px;
    }
    & .noUi-target{
        background-color: #191C3C;
        border-radius: 12px;
        border: 0;
        box-shadow: none;
    }

    & .noUi-connect{
        background-color: var(--tp-theme-primary);
    }

    & .noUi-horizontal{
        height: 6px;
    }
    & .noUi-handle{
        height: 24px;
        width: 24px;
        background-color: var(--tp-theme-primary);
        border-radius: 50%;
        border: 4px solid var(--tp-common-white);
        box-shadow: 0px 4px 10px rgba(5, 9, 43, 0.3);
        top: -9px;

        &:hover{
            cursor: pointer;
        }
        &::before,
        &::after{
            display: none;
        }
    }

    &-dark{
        

        & .noUi-handle{
            border: 4px solid #2D314B;
        }
    }
}


/* tp swiper slider dot */

.#{$theme-prifix}-swiper-dot{
    line-height: 1;
    .swiper-pagination-bullet{
        width: 10px;
        height: 10px;
        background-color: var(--tp-grey-3);
        opacity: 1;
        margin: 0 3px;
        position: relative;
        @extend %transition;
        & button{
            font-size: 0;
        }
        
        &.swiper-pagination-bullet-active{
            background-color: var(--tp-theme-primary);
        }
    }

    &.#{$theme-prifix}-swiper-dot-style-darkRed{  
        & .swiper-pagination-bullet{
            &.swiper-pagination-bullet-active{
                background-color: var(--tp-theme-secondary);
            }
        }      
    }
}

.#{$theme-prifix}-swiper-dot-border{
    line-height: 1;
    .swiper-pagination-bullet{
        width: 12px;
        height: 12px;
        background-color: transparent;
        opacity: 1;
        margin: 0 3px;
        position: relative;
        @extend %transition;
        border: 1px solid rgba($color: $black, $alpha: .4);
        & button{
            font-size: 0;
        }
        
        &.swiper-pagination-bullet-active{
            background-color: var(--tp-common-black);
            border-color: var(--tp-common-black);
        }

        &:hover{
            border-color: var(--tp-common-black);
        }
    }

    &.#{$theme-prifix}-swiper-dot-style-darkRed{  
        & .swiper-pagination-bullet{
            &.swiper-pagination-bullet-active{
                background-color: var(--tp-theme-secondary);
            }
        }      
    }
}

.#{$theme-prifix}-swiper-arrow{
    & button{
        width: 40px;
        height: 40px;
        line-height: 38px;
        font-size: 20px;
        text-align: center;
        background-color: transparent;
        color: var(--tp-text-2);
        border: 1px solid rgba($color: $black, $alpha: .1);
        border-radius: 50%;
        &:hover{
            background-color: var(--tp-theme-primary);
            color: var(--tp-common-white);
        }

        & svg{
            @extend %svg-2;
        }
    }
}


/* tp swiper scrollbar */

.#{$theme-prifix}-swiper-scrollbar{
    background-color: #EDEFF2;
    height: 2px;
    @extend %transition;
    & .#{$theme-prifix}-swiper-scrollbar-drag{
        background-color: var(--tp-common-black);
        height: 100%;

        &:hover{
            cursor: pointer;
        }
    }

    &:hover,
    &:focus{
        @include transform(scaleY(3));
    }
}

/* tp slick arrow */

.#{$theme-prifix}-slick-dot{
    & .slick-dots{
        & li{
            display: inline-block;
            margin: 0 4px;
            &.slick-active{
                & button{
                    background-color: var(--tp-common-white);
                }
            }
            & button{
                font-size: 0;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background-color: rgba($color: $white, $alpha: .2);
            }
        }
    }
    &-blue{
        & .slick-dots{
            & li{
                &.slick-active{
                    & button{
                        background-color: var(--tp-theme-primary);
                    }
                }
                & button{
                    background-color: var(--tp-grey-3);
                }
            }
        }
    }
}

