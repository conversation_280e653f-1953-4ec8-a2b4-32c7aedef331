@use '../../utils' as *;

/*----------------------------------------*/
/*  3.1 Header Style 1
/*----------------------------------------*/

.container{
    transition: all 1s;
}
.tp-transparent{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
}
.header-sticky{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    opacity: 1;
    width: 100%;
    z-index: 999;
    visibility: visible;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0px 1px 3px 0px rgba(18, 20, 32, 0.14);
    -webkit-animation: 0.95s ease 0s normal forwards 1 running headerSlideDown;
    animation: 0.95s ease 0s normal forwards 1 running headerSlideDown;
    & .ab-logo-1{
        display: none;
    }
    & .ab-logo-2{
        display: block;
    }
    &::after{
        position: absolute;
        content: '';
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        backdrop-filter: blur(10px);
        z-index: -1;
    }
}
.ab-logo-2{
    display: none;
}

.tp-header-style-9{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.logo-2{
    display: none;
}
.tp-header{
    &-logo{
        & img{
            width: 85px;
            height: 100%;
        }
    }
    &-area{
        &.header-sticky{
            & .container{
                max-width: 100%;
            }
            & .tp-header-menu > nav > ul > li > a {
                padding: 27px 0;
            }
        }
    }
}
.tp-header{
    &-mob-space{
        @media #{$lg,$md,$xs}{
            padding: 20px 0px;
        }
    }
    &-area{
        @media #{$xl}{
            padding-left: 30px;
            padding-right: 30px;
        }
        @media #{$lg,$md,$xs}{
            padding-left: 0px;
            padding-right: 0px;
        }
    }
    &-menu{
        & > nav{
            & > ul{
                & > li{
                    display: inline-block;
                    list-style-type: none;
                    margin: 0px 25px;
                    & > a{
                        padding: 40px 0;
                        display: inline-block;
                        font-weight: 500;
                        font-size: 16px;
                        line-height: 1;
                        text-transform: uppercase;
                        color: var(--tp-common-black);
                    }
                }
            }
        }
    }
    &-bar{
        line-height: 0;
        & button{
            & span{
                height: 2px;
                width: 40px;
                background-color: var(--tp-common-black);
                display: block;
                margin: 6px 0;
            }
            &:hover{
                & span{
                    animation: bar_anim 0.8s cubic-bezier(0.44, 1.1, 0.53, 0.99) 1 forwards;
                    &:nth-child(2) {
                        animation-delay: 0.1s;
                    }
                }
            }
        }
    }
}