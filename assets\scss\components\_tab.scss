@use '../utils' as *;

/*----------------------------------------*/
/*  2.13 Tab
/*----------------------------------------*/

.tp-tab{
    & .nav-tabs{
        padding: 0;
        margin: 0;
        border: 0;

        & .nav-link{
            padding: 0;
            margin: 0;
            border: 0;
        }
    }
}
.tp-product{
    &-tab-2{
        & .nav-tabs{
            & .nav-link{
                font-size: 20px;
                color: #A0A2A4;
                position: relative;
    
                @media #{$xs}{
                    font-size: 15px;
                }
                &:not(:first-child){
                    margin-left: 28px;
                    &::after{
                        position: absolute;
                        left: -17px;
                        top: 50%;
                        @extend %translateY2;
                        content: '';
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background-color: #CED2D6;
                    }
                }
                &.active{
                    color: var(--tp-theme-secondary);
                    .#{$theme-prifix}-product-tab-tooltip{
                        opacity: 1;
                        visibility: visible;
                    }
                }
    
                & .#{$theme-prifix}-product-tab-tooltip{
                    position: absolute;
                    top: -24px;
                    right: 0;
                    background-color: var(--tp-theme-secondary);
                    color: var(--tp-common-white);
                    font-size: 12px;
                    line-height: 1;
                    display: inline-block;
                    padding: 4px 9px;
                    border-radius: 4px;
                    visibility: hidden;
                    opacity: 0;
                    @extend %transition;
                    font-weight: 700;
    
                    &::after{
                        position: absolute;
                        content: '';
                        bottom: 0;
                        bottom: -5px;
                        left: 7px;
                        width: 13px;
                        height: 6px;
                        background-color: var(--tp-theme-secondary);
                        clip-path: polygon(100% 0, 0 0, 28% 100%);
                    }
                }
            }
        }
    }
    &-tab-3{
        & .nav-tabs{
            & .nav-link{
                font-size: 16px;
                padding: 0 0;
                @media #{$xs}{
                    font-size: 15px;
                }
                &:not(:first-child){
                    margin-left: 23px;
                    &::after{
                        width: 4px;
                        height: 4px;
                        left: -14px;
                    }
                }
                &.active{
                    color: var(--tp-common-black);
                    .#{$theme-prifix}-product-tab-tooltip{
                        opacity: 1;
                        visibility: visible;
                    }
                }
    
                & .#{$theme-prifix}-product-tab-tooltip{
                    background-color: var(--tp-theme-primary);

                    &::after{
                        background-color: var(--tp-theme-primary);
                    }
                }
            }
        }
    }
    &-tab-5{
        & .nav-tabs{
            & .nav-link{
                padding: 0 5px;
                @media #{$xs}{
                    font-size: 15px;
                }

                &:not(:last-child){
                    &::after{
                        left: -15px;
                    }
                }

                &:not(:first-child){
                    margin-left: 22px;
                }

                &.active{
                    color: var(--tp-common-black);
                    .#{$theme-prifix}-product-tab-tooltip{
                        opacity: 1;
                        visibility: visible;
                    }
                }
    
                & .#{$theme-prifix}-product-tab-tooltip{
                    background-color: var(--tp-theme-green);

                    &::after{
                        background-color: var(--tp-theme-green);
                    }
                }
            }
        }
    }
}
.tp-tab-line{
    position: absolute;
    content: "";
    left: 0;
    bottom: -1px;
    width: 38%;
    height: 2px;
    background-color: var(--tp-common-black);
    @extend %transition;
}