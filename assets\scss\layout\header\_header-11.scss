@use '../../utils' as *;

/*----------------------------------------*/
/*  3.11 Header Style 11
/*----------------------------------------*/

.tp-inner-header{
    &-style-2{
        & .tp-inner-header-menu nav > ul > li > a {
            color: var(--tp-common-black);
        }
        & .tp-inner-header-right-action ul li .tp-inner-bar {
            color: var(--tp-common-black);
        }
        & .tp-inner-header-right-action ul li .tp-inner-cart {
            color: var(--tp-common-black);
        }
        & .tp-inner-header-right-action ul li .tp-inner-cart a span i {
            color: var(--tp-common-white);
            background-color: var(--tp-common-black);
        }
    }
    &-style-3{
        & .tp-inner-header-right-action ul::after {
            display: none;
        }
    }
    &-menu{
        & > nav{
            & > ul{
                & > li{
                    text-decoration: none;
                    display: inline-block;
                    margin: 0px 25px;
                    & > a{
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 1;
                        padding: 45px 0px;
                        display: inline-block;
                        text-transform: uppercase;
                        color: var(--tp-common-white);
                        font-family: var(--tp-ff-syne);
                    }
                }
            }
        }
    }
    &-mob-space{
        @media #{$lg,$md,$xs}{
            padding: 20px 0;
        }
    }
    &-right-action{
        & ul{
            position: relative;
            margin-left: 30px;
            @media #{$md,$xs}{
                margin-left: 0;
            }
            & li{
                display: inline-block;
                margin-left: 40px;
                & .tp-inner-cart{
                    color: var(--tp-common-white);
                    & a{
                        & span{
                            & i{
                                position: absolute;
                                top: -10px;
                                right: -13px;
                                height: 22px;
                                width: 22px;
                                line-height: 22px;
                                border-radius: 50%;
                                text-align: center;
                                display: inline-block;
                                font-style: normal;
                                color: var(--tp-common-black);
                                background-color: var(--tp-common-white);
                            }
                        }
                    }
                }
                & .tp-inner-bar{
                    color: var(--tp-common-white);
                }
            }
        }
    }
} 
.tp-inner-header-border{
    border-bottom: 1px solid rgba(20, 20, 20, 0.10);
}
.tp-inner-header-height{
    & .tp-inner-header-menu nav ul li a {
        padding: 32px 0px;
    }
}
.tp-inner-header-2{
    &-bg{
        backdrop-filter: blur(4px);
        background: rgba(255, 255, 255, 0.7);
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
        & .tp-shop-mob-search span {
            color: var(--tp-common-black);
        }
    }
    &-menu{
        & > nav{
            & > ul{
                & > li{
                    list-style-type: none;
                    display: inline-block;
                    margin: 0px 22px;
                    @media #{$xl}{
                        margin: 0px 15px;
                    }
                    & > a{
                        font-size: 18px;
                        font-weight: 400;
                        line-height: 1;
                        padding: 41px 0;
                        display: inline-block;
                        color: var(--tp-common-black);
                        font-family: var(--tp-ff-marcellus);
                    }
                }
            }
        }
    }
    &-right{
        & button,
        & a{
            margin-left: 30px;
            @media #{$xs}{
                margin-left: 20px;
            }
        }
    }
    &-cart{
        height: 42px;
        width: 42px;
        line-height: 36px;
        text-align: center;
        display: inline-block;
        border-radius: 50%;
        border: 1px solid #EEE;
        flex: 0 0 auto;
        & span{
            color: var(--tp-common-white);
        }
        &:hover{
            border-color: var(--tp-common-white);
            background-color: var(--tp-common-white);
            & span{
                color: var(--tp-common-black);              
            }
        }
    }
    &-wishlist{
        & i{
            position: absolute;
            top: -4px;
            right: -8px;
            height: 16px;
            width: 16px;
            line-height: 14px;
            border-radius: 50%;
            text-align: center;
            font-style: normal;
            display: inline-block;
            color: var(--tp-common-white);
            background-color: var(--tp-common-black);
        }
        & span{
            color: var(--tp-common-black);
        }
    }
    &-search{
        & input{
            height: 42px;
            border-radius: 30px;
            background-color: transparent;
            border: 1px solid #EEE;
            padding: 0px 30px;
            padding-left: 45px;
            color: var(--tp-common-black);
            font-family: var(--tp-ff-marcellus);
            font-size: 16px;
            font-weight: 400;
            @include placeholder{
                color: var(--tp-common-black);
                font-family: var(--tp-ff-marcellus);
                font-size: 16px;
                font-weight: 400;
            }
            &:focus{
                border-color: 1px solid var(--tp-common-black);
            }
        }
        & span{
            position: absolute;
            top: 48%;
            left: 20px;
            transform: translateY(-50%);
            color: var(--tp-common-black);
        }
    }
}
.tp-inner-header{
    &-white{
        & .tp-inner-header-2-menu > nav > ul > li > a {
            color: var(--tp-common-white);
        }
        & .tp-inner-header-2-wishlist span {
            color: var(--tp-common-white);
        }
        & .tp-inner-header-2-wishlist i {
            color: var(--tp-common-black);
            background-color: var(--tp-common-white);
        }
        & .tp-inner-header-2-bar{
            & span{
                color: var(--tp-common-white);
            }
        }
        & .tp-inner-header-2-cart {
            & span {
                color: var(--tp-common-white);
                transition: .3s;
            }
            &:hover{
                & span{
                    color: var(--tp-common-black);
                }
            }
        }
        & .tp-inner-header-2-search input {
            border: 1px solid rgba(255, 255, 255, 0.20);
            color: var(--tp-common-white);
            @include placeholder{
                color: var(--tp-common-white);
            }
            &:focus{
                border-color: var(--tp-common-white);
            }
        }
        & .tp-inner-header-2-search span {
            color: var(--tp-common-white);
        }
    }
}
.tp-inner-header-2-bg{
    & .tp-inner-header-2-cart span {
        color: var(--tp-common-black);
    }
}
.header-sticky{
    &.tp-inner-header-area{
        & .tp-inner-header-menu > nav > ul > li > a {
            padding: 25px 0px;
            color: var(--tp-common-black);
        }
        & .tp-inner-header-right-action ul li .tp-inner-cart {
            color: var(--tp-common-black);
        }
        & .tp-inner-header-right-action ul li .tp-inner-cart a span i {
            color: var(--tp-common-white);
            background-color: var(--tp-common-black);
        }
        & .tp-inner-header-right-action ul li .tp-inner-bar {
            color: var(--tp-common-black);
        }
    }
}