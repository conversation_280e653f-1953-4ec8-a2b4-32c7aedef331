@use '../utils' as *;

/*----------------------------------------*/
/*  2.5 Preloader
/*----------------------------------------*/

.preloader-wrap {
	background-color: #000000;
	height: 100%;
	width: 100%;
	position: fixed;
	z-index: 999999;
	margin-top: 0px;
	top: 0px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.preloader-2 {
	display: inline-block;
}
.loader-text{
	font-size: 20px;
	color: var(--tp-common-white);
	margin-top: 10px;
	opacity: .6;
}

.preloader-2 .line {
	width: 2px;
	height: 40px;
	background: #fff;
	margin: 0 4px;
	display: inline-block;
	animation: opacity-2 1000ms infinite ease-in-out;
}

.preloader-2 .line-1 {
	animation-delay: 800ms;
}

.preloader-2 .line-2 {
	animation-delay: 600ms;
}

.preloader-2 .line-3 {
	animation-delay: 400ms;
}

.preloader-2 .line-4 {
	animation-delay: 200ms;
}

.preloader-2 .line-6 {
	animation-delay: 200ms;
}

.preloader-2 .line-7 {
	animation-delay: 400ms;
}

.preloader-2 .line-8 {
	animation-delay: 600ms;
}

.preloader-2 .line-9 {
	animation-delay: 800ms;
}



@keyframes opacity-2 {
	0% {
		opacity: 1;
		height: 40px;
	}

	50% {
		opacity: 0;
		height: 24px;
	}

	100% {
		opacity: 1;
		height: 40px;
	}
}
  