@use '../../utils' as *;

/*----------------------------------------*/
/*  3.2 Header Style 2
/*----------------------------------------*/

.tp-header-2{
    &-area{
        &.header-sticky{
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            opacity: 1;
            width: 100%;
            z-index: 999;
            visibility: visible;
            background: rgba(255, 255, 255, 0.70);
            box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.05);
            animation: 0.95s ease 0s normal forwards 1 running headerSlideDown;
            &::after{
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                backdrop-filter: blur(4px);
                z-index: -1;
            }
        }
    }
}

.tp-header-2{
    &-ptb{
        padding: 15px 0;
    }
    &-space{
        padding: 35px 0;
    }
    &-menu-bar{
        line-height: 0;
        & button{
            & span{
                height: 2px;
                width: 80px;
                background-color: var(--tp-common-black);
                display: block;
                margin: 8px auto;
                @media #{$md,$xs}{
                    width: 45px;
                }
            }
            &:hover{
                & span{
                    animation: bar_anim 0.8s cubic-bezier(0.44, 1.1, 0.53, 0.99) 1 forwards;
                    &:nth-child(2) {
                        animation-delay: 0.1s;
                    }
                }
            }
        }
    }
    &-button{
        overflow: hidden;
        width: 180px;
        margin-left: auto;
        line-height: 0;
    }
    &-cart{
        & button{
            font-size: 18px;
            font-weight: 400;
            line-height: 14px;
            color: var(--tp-common-black);
            font-family: var(--tp-ff-body);
            & span{
                height: 45px;
                width: 45px;
                line-height: 37px;
                border: 1px solid;
                text-align: center;
                border-radius: 50%;
                display: inline-block;
                margin-left: 7px;
            }
        }
    }
}